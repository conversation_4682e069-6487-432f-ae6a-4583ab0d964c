<!DOCTYPE html>
<html lang="id">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><PERSON>k <PERSON> Air</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      background: white;
      border-radius: 15px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .header {
      background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
      color: white;
      padding: 30px;
      text-align: center;
    }

    .header h1 {
      font-size: 2.5em;
      margin-bottom: 10px;
    }

    .header p {
      font-size: 1.1em;
      opacity: 0.9;
    }

    .form-container {
      padding: 40px;
    }

    .form-group {
      margin-bottom: 25px;
    }

    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 600;
      color: #2c3e50;
      font-size: 1.1em;
    }

    input[type="text"] {
      width: 100%;
      padding: 15px;
      border: 2px solid #ecf0f1;
      border-radius: 8px;
      font-size: 1.1em;
      transition: border-color 0.3s;
    }

    input[type="text"]:focus {
      outline: none;
      border-color: #3498db;
    }

    .btn {
      background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
      color: white;
      padding: 15px 30px;
      border: none;
      border-radius: 8px;
      font-size: 1.1em;
      cursor: pointer;
      transition: transform 0.2s;
      width: 100%;
    }

    .btn:hover {
      transform: translateY(-2px);
    }

    .hasil {
      margin-top: 30px;
      padding: 20px;
      border-radius: 8px;
      background: #f8f9fa;
    }

    .info-pelanggan {
      background: #ecf0f1;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 25px;
    }

    .info-pelanggan h3 {
      margin: 0 0 15px 0;
      color: #2c3e50;
      font-size: 1.3em;
    }

    .info-pelanggan p {
      margin: 8px 0;
      font-size: 1em;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }

    th, td {
      padding: 12px;
      text-align: left;
      border-bottom: 1px solid #ecf0f1;
    }

    th {
      background: #34495e;
      color: white;
      font-weight: 600;
      text-transform: uppercase;
      font-size: 0.9em;
    }

    tr:hover {
      background: #f8f9fa;
    }

    .error {
      color: #e74c3c;
      background: #fadbd8;
      padding: 15px;
      border-radius: 8px;
      border-left: 4px solid #e74c3c;
    }

    .loading {
      color: #3498db;
      background: #d6eaf8;
      padding: 15px;
      border-radius: 8px;
      border-left: 4px solid #3498db;
    }

    @media (max-width: 768px) {
      .container {
        margin: 10px;
        border-radius: 10px;
      }

      .header {
        padding: 20px;
      }

      .header h1 {
        font-size: 2em;
      }

      .form-container {
        padding: 20px;
      }

      table {
        font-size: 0.9em;
      }

      th, td {
        padding: 8px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🚰 Cek Tagihan Air</h1>
      <p>Masukkan nomor sambungan untuk melihat tagihan Anda</p>
    </div>

    <div class="form-container">
      <form id="cekForm">
        <div class="form-group">
          <label for="nosal">Nomor Sambungan:</label>
          <input type="text" id="nosal" name="nosal" placeholder="Contoh: 0101010020" required>
        </div>
        <button type="submit" class="btn">🔍 Cek Tagihan</button>
      </form>

      <div id="hasil" class="hasil" style="display: none;"></div>
    </div>
  </div>

  <script>
    const API_URL = "https://satupintu.kopkar.my.id/api/proxy-cektagihan.php";
    const API_KEY = "e8a3b5c7d9f1g2h4i5j6k7l8m9n0o1p2"; // ganti dengan API_SECRET_KEY anda

    document.getElementById("cekForm").addEventListener("submit", async function(e) {
      e.preventDefault();
      const nosal = document.getElementById("nosal").value.trim();
      const hasilDiv = document.getElementById("hasil");

      // Tampilkan loading
      hasilDiv.style.display = "block";
      hasilDiv.innerHTML = '<div class="loading">🔄 Sedang mencari data tagihan...</div>';

      try {
        // Coba dengan header X-API-KEY terlebih dahulu
        let response;
        let result;

        try {
          response = await fetch(API_URL, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "X-API-KEY": API_KEY
            },
            body: JSON.stringify({ nosambungan: nosal })
          });
          result = await response.json();
        } catch (corsError) {
          console.log("CORS error dengan X-API-KEY, mencoba tanpa header...");

          // Jika gagal karena CORS, coba tanpa header X-API-KEY
          response = await fetch(API_URL, {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              nosambungan: nosal,
              api_key: API_KEY  // Kirim API key dalam body
            })
          });
          result = await response.json();
        }

        if (result.success && result.plg && result.plg.tagihan && result.plg.tagihan.length > 0) {
          const plg = result.plg;

          // Tampilkan informasi pelanggan
          let infoHTML = `
            <div class="info-pelanggan">
              <h3>📋 Informasi Pelanggan</h3>
              <p><strong>No. Sambungan:</strong> ${plg.nosal}</p>
              <p><strong>Nama:</strong> ${plg.nama}</p>
              <p><strong>Alamat:</strong> ${plg.alamat}</p>
              <p><strong>Unit:</strong> ${plg.unit}</p>
            </div>
          `;

          // Tampilkan tabel tagihan
          let table = `
            <h3 style="margin-bottom: 15px; color: #2c3e50;">💧 Detail Tagihan</h3>
            <table>
              <thead>
                <tr>
                  <th>Periode</th>
                  <th>Stand Awal</th>
                  <th>Stand Akhir</th>
                  <th>Pakai (m³)</th>
                  <th>Golongan</th>
                  <th>Harga Air</th>
                  <th>Biaya Langganan</th>
                  <th>Angsuran</th>
                  <th>Denda</th>
                  <th>Biaya Admin</th>
                  <th>Total</th>
                </tr>
              </thead>
              <tbody>
          `;

          plg.tagihan.forEach(row => {
            const formatRupiah = (amount) => {
              const num = parseFloat(amount) || 0;
              return new Intl.NumberFormat('id-ID', {
                style: 'currency',
                currency: 'IDR',
                minimumFractionDigits: 0
              }).format(num);
            };

            const biayaAdmin = 11000;
            const totalDenganAdmin = parseFloat(row.total) + biayaAdmin;

            table += `
              <tr>
                <td><strong>${row.periode}</strong></td>
                <td>${row.stand_awal}</td>
                <td>${row.stand_akhir}</td>
                <td><strong>${row.pakai}</strong></td>
                <td><span style="background: #3498db; color: white; padding: 2px 8px; border-radius: 4px; font-size: 0.9em;">${row.golongan}</span></td>
                <td>${formatRupiah(row.harga_air)}</td>
                <td>${formatRupiah(row.biaya_langgan)}</td>
                <td>${formatRupiah(row.angsuran)}</td>
                <td>${formatRupiah(row.denda)}</td>
                <td><span style="background: #e67e22; color: white; padding: 2px 8px; border-radius: 4px; font-size: 0.9em;">${formatRupiah(biayaAdmin)}</span></td>
                <td><strong style="color: #e74c3c; font-size: 1.1em;">${formatRupiah(totalDenganAdmin)}</strong></td>
              </tr>
            `;
          });

          table += `
              </tbody>
            </table>
          `;

          hasilDiv.innerHTML = infoHTML + table;
        } else {
          hasilDiv.innerHTML = '<div class="error">❌ Data tidak ditemukan. Pastikan nomor sambungan sudah benar.</div>';
        }
      } catch (error) {
        console.error('Error:', error);
        hasilDiv.innerHTML = `<div class="error">⚠️ Terjadi kesalahan: ${error.message}</div>`;
      }
    });

    // Auto focus pada input
    document.getElementById("nosal").focus();
  </script>
</body>
</html>