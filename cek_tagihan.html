<!DOCTYPE html>
<html lang="id">
<head>
  <meta charset="UTF-8">
  <title>Cek <PERSON></title>
  <style>
    body {
      font-family: Arial, sans-serif;
      background: #f4f6f9;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 800px;
      margin: 40px auto;
      background: #fff;
      padding: 20px;
      border-radius: 12px;
      box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    }
    h2 {
      text-align: center;
      margin-bottom: 20px;
      color: #2c3e50;
    }
    form {
      display: flex;
      justify-content: center;
      margin-bottom: 20px;
    }
    input[type="text"] {
      padding: 10px;
      width: 60%;
      border: 1px solid #ccc;
      border-radius: 8px 0 0 8px;
      outline: none;
    }
    button {
      padding: 10px 20px;
      border: none;
      background: #3498db;
      color: #fff;
      border-radius: 0 8px 8px 0;
      cursor: pointer;
    }
    button:hover {
      background: #2980b9;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 15px;
    }
    table, th, td {
      border: 1px solid #ddd;
    }
    th {
      background: #3498db;
      color: #fff;
      padding: 10px;
    }
    td {
      padding: 10px;
      text-align: center;
    }
    .error {
      color: red;
      text-align: center;
      margin-top: 10px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h2>Cek Tagihan PDAM</h2>
    <form id="cekForm">
      <input type="text" id="nosal" placeholder="Masukkan No. Sal" required>
      <button type="submit">Cek Tagihan</button>
    </form>
    <div id="hasil"></div>
  </div>

  <script>
    const API_URL = "https://satupintu.kopkar.my.id/api/proxy-cektagihan.php";
    const API_KEY = "e8a3b5c7d9f1g2h4i5j6k7l8m9n0o1p2"; // ganti dengan API_SECRET_KEY anda

    document.getElementById("cekForm").addEventListener("submit", async function(e) {
      e.preventDefault();
      const nosal = document.getElementById("nosal").value.trim();
      const hasilDiv = document.getElementById("hasil");
      hasilDiv.innerHTML = "Sedang mencari data...";

      try {
        const response = await fetch(API_URL, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-API-KEY": API_KEY
          },
          body: JSON.stringify({ nosal: nosal })
        });

        const result = await response.json();

        if (result.status === "success" && result.data.length > 0) {
          let table = `
            <table>
              <tr>
                <th>Periode</th>
                <th>Nama</th>
                <th>Pakai (m³)</th>
                <th>Harga Air</th>
                <th>Total</th>
              </tr>
          `;
          result.data.forEach(row => {
            table += `
              <tr>
                <td>${row.periode}</td>
                <td>${row.nama}</td>
                <td>${row.pakai}</td>
                <td>Rp ${parseInt(row.harga_air).toLocaleString()}</td>
                <td><b>Rp ${parseInt(row.total).toLocaleString()}</b></td>
              </tr>
            `;
          });
          table += "</table>";
          hasilDiv.innerHTML = table;
        } else {
          hasilDiv.innerHTML = `<p class="error">${result.message}</p>`;
        }
      } catch (error) {
        hasilDiv.innerHTML = `<p class="error">Terjadi kesalahan: ${error.message}</p>`;
      }
    });
  </script>
</body>
</html>
