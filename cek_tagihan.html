<!DOCTYPE html>
<html lang="id">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><PERSON>k <PERSON> Air</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      background: white;
      border-radius: 15px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .header {
      background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
      color: white;
      padding: 30px;
      text-align: center;
    }

    .header h1 {
      font-size: 2.5em;
      margin-bottom: 10px;
    }

    .header p {
      font-size: 1.1em;
      opacity: 0.9;
    }

    .form-container {
      padding: 40px;
    }

    .form-group {
      margin-bottom: 25px;
    }

    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 600;
      color: #2c3e50;
      font-size: 1.1em;
    }

    input[type="text"] {
      width: 100%;
      padding: 15px;
      border: 2px solid #ecf0f1;
      border-radius: 8px;
      font-size: 1.1em;
      transition: border-color 0.3s;
    }

    input[type="text"]:focus {
      outline: none;
      border-color: #3498db;
    }

    .btn {
      background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
      color: white;
      padding: 15px 30px;
      border: none;
      border-radius: 8px;
      font-size: 1.1em;
      cursor: pointer;
      transition: transform 0.2s;
      width: 100%;
    }

    .btn:hover {
      transform: translateY(-2px);
    }

    .hasil {
      margin-top: 30px;
      padding: 20px;
      border-radius: 8px;
      background: #f8f9fa;
    }

    .info-pelanggan {
      background: #ecf0f1;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 25px;
    }

    .info-pelanggan h3 {
      margin: 0 0 15px 0;
      color: #2c3e50;
      font-size: 1.3em;
    }

    .info-pelanggan p {
      margin: 8px 0;
      font-size: 1em;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }

    th, td {
      padding: 12px;
      text-align: left;
      border-bottom: 1px solid #ecf0f1;
    }

    th {
      background: #34495e;
      color: white;
      font-weight: 600;
      text-transform: uppercase;
      font-size: 0.9em;
    }

    tr:hover {
      background: #f8f9fa;
    }

    .error {
      color: #e74c3c;
      background: #fadbd8;
      padding: 15px;
      border-radius: 8px;
      border-left: 4px solid #e74c3c;
    }

    .loading {
      color: #3498db;
      background: #d6eaf8;
      padding: 15px;
      border-radius: 8px;
      border-left: 4px solid #3498db;
    }

    .btn-cetak {
      background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
      color: white;
      padding: 12px 25px;
      border: none;
      border-radius: 8px;
      font-size: 1em;
      cursor: pointer;
      transition: transform 0.2s;
      margin-top: 15px;
      display: inline-block;
    }

    .btn-cetak:hover {
      transform: translateY(-2px);
    }

    @media print {
      .container .header,
      .form-container form,
      .btn-cetak {
        display: none;
      }

      body * {
        visibility: hidden;
      }

      .print-area, .print-area * {
        visibility: visible;
      }

      .print-area {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        font-family: Arial, sans-serif;
        font-size: 12px;
        line-height: 1.2;
        padding: 20px;
      }

      .print-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        border-bottom: 3px solid #000;
        padding-bottom: 15px;
      }

      .print-logo {
        width: 80px;
        height: 80px;
        margin-right: 20px;
        background: #2e8b57;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 10px;
        text-align: center;
        line-height: 1.1;
      }

      .print-header-text {
        flex: 1;
        text-align: center;
      }

      .print-header h1 {
        font-size: 14px;
        font-weight: bold;
        margin: 2px 0;
      }

      .print-header h2 {
        font-size: 16px;
        font-weight: bold;
        margin: 2px 0;
      }

      .print-header p {
        font-size: 10px;
        margin: 1px 0;
      }

      .print-title {
        text-align: center;
        font-weight: bold;
        font-size: 14px;
        margin: 15px 0;
        text-decoration: underline;
      }

      .detail-pelanggan {
        margin: 15px 0;
      }

      .detail-pelanggan h3 {
        background: #333;
        color: white;
        padding: 4px 8px;
        margin: 0 0 0 0;
        font-size: 11px;
        font-weight: bold;
        text-transform: uppercase;
      }

      .detail-table {
        width: 100%;
        border: 1px solid #000;
        border-collapse: collapse;
        margin-bottom: 15px;
      }

      .detail-table td {
        border: 1px solid #000;
        padding: 4px 8px;
        font-size: 10px;
        vertical-align: top;
      }

      .detail-table .label {
        width: 120px;
        font-weight: bold;
        background: #f5f5f5;
      }

      .detail-table .dots {
        border-bottom: 1px dotted #000;
        min-height: 15px;
      }

      .tagihan-section h3 {
        background: #333;
        color: white;
        padding: 4px 8px;
        margin: 15px 0 0 0;
        font-size: 11px;
        font-weight: bold;
        text-transform: uppercase;
      }

      .tagihan-table {
        width: 100%;
        border: 1px solid #000;
        border-collapse: collapse;
        margin-top: 0;
      }

      .tagihan-table th,
      .tagihan-table td {
        border: 1px solid #000;
        padding: 4px;
        text-align: center;
        font-size: 9px;
        font-weight: normal;
      }

      .tagihan-table th {
        background: #333;
        color: white;
        font-weight: bold;
        text-transform: uppercase;
      }

      .tagihan-table .total-row {
        background: #e0e0e0;
        font-weight: bold;
      }

      .tagihan-table .total-row td:first-child {
        text-align: center;
        font-weight: bold;
      }
    }

    @media (max-width: 768px) {
      .container {
        margin: 10px;
        border-radius: 10px;
      }

      .header {
        padding: 20px;
      }

      .header h1 {
        font-size: 2em;
      }

      .form-container {
        padding: 20px;
      }

      table {
        font-size: 0.9em;
      }

      th, td {
        padding: 8px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🚰 Cek Tagihan Air</h1>
      <p>Masukkan nomor sambungan untuk melihat tagihan Anda</p>
    </div>

    <div class="form-container">
      <form id="cekForm">
        <div class="form-group">
          <label for="nosal">Nomor Sambungan:</label>
          <input type="text" id="nosal" name="nosal" placeholder="Contoh: 0101010020" required>
        </div>
        <button type="submit" class="btn">🔍 Cek Tagihan</button>
      </form>

      <div id="hasil" class="hasil" style="display: none;"></div>
    </div>
  </div>

  <script>
    const API_URL = "https://satupintu.kopkar.my.id/api/proxy-cektagihan.php";
    const API_KEY = "e8a3b5c7d9f1g2h4i5j6k7l8m9n0o1p2"; // ganti dengan API_SECRET_KEY anda

    document.getElementById("cekForm").addEventListener("submit", async function(e) {
      e.preventDefault();
      const nosal = document.getElementById("nosal").value.trim();
      const hasilDiv = document.getElementById("hasil");

      // Tampilkan loading
      hasilDiv.style.display = "block";
      hasilDiv.innerHTML = '<div class="loading">🔄 Sedang mencari data tagihan...</div>';

      try {
        // Coba dengan header X-API-KEY terlebih dahulu
        let response;
        let result;

        try {
          response = await fetch(API_URL, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "X-API-KEY": API_KEY
            },
            body: JSON.stringify({ nosambungan: nosal })
          });
          result = await response.json();
        } catch (corsError) {
          console.log("CORS error dengan X-API-KEY, mencoba tanpa header...");

          // Jika gagal karena CORS, coba tanpa header X-API-KEY
          response = await fetch(API_URL, {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              nosambungan: nosal,
              api_key: API_KEY  // Kirim API key dalam body
            })
          });
          result = await response.json();
        }

        if (result.success && result.plg && result.plg.tagihan && result.plg.tagihan.length > 0) {
          const plg = result.plg;

          // Tampilkan informasi pelanggan
          let infoHTML = `
            <div class="info-pelanggan">
              <h3>📋 Informasi Pelanggan</h3>
              <p><strong>No. Sambungan:</strong> ${plg.nosal}</p>
              <p><strong>Nama:</strong> ${plg.nama}</p>
              <p><strong>Alamat:</strong> ${plg.alamat}</p>
              <p><strong>Unit:</strong> ${plg.unit}</p>
            </div>
          `;

          // Tampilkan tabel tagihan
          let table = `
            <h3 style="margin-bottom: 15px; color: #2c3e50;">💧 Detail Tagihan</h3>
            <table>
              <thead>
                <tr>
                  <th>Periode</th>
                  <th>Stand Awal</th>
                  <th>Stand Akhir</th>
                  <th>Pakai (m³)</th>
                  <th>Golongan</th>
                  <th>Harga Air</th>
                  <th>Biaya Langganan</th>
                  <th>Angsuran</th>
                  <th>Denda</th>
                  <th>Dana Meter</th>
                  <th>Biaya Admin</th>
                  <th>Total</th>
                </tr>
              </thead>
              <tbody>
          `;

          plg.tagihan.forEach(row => {
            const formatRupiah = (amount) => {
              const num = parseFloat(amount) || 0;
              return new Intl.NumberFormat('id-ID', {
                style: 'currency',
                currency: 'IDR',
                minimumFractionDigits: 0
              }).format(num);
            };

            const biayaAdmin = 11000;
            const danaMeter = 1000;
            const totalDenganTambahan = parseFloat(row.total) + biayaAdmin + danaMeter;

            // Hitung pemakaian yang benar: Stand Akhir - Stand Awal
            const standAwal = parseInt(row.stand_awal) || 0;
            const standAkhir = parseInt(row.stand_akhir) || 0;
            const pakaiSebenarnya = standAkhir - standAwal;

            table += `
              <tr>
                <td><strong>${row.periode}</strong></td>
                <td>${row.stand_awal}</td>
                <td>${row.stand_akhir}</td>
                <td><strong>${pakaiSebenarnya}</strong></td>
                <td><span style="background: #3498db; color: white; padding: 2px 8px; border-radius: 4px; font-size: 0.9em;">${row.golongan}</span></td>
                <td>${formatRupiah(row.harga_air)}</td>
                <td>${formatRupiah(row.biaya_langgan)}</td>
                <td>${formatRupiah(row.angsuran)}</td>
                <td>${formatRupiah(row.denda)}</td>
                <td><span style="background: #9b59b6; color: white; padding: 2px 8px; border-radius: 4px; font-size: 0.9em;">${formatRupiah(danaMeter)}</span></td>
                <td><span style="background: #e67e22; color: white; padding: 2px 8px; border-radius: 4px; font-size: 0.9em;">${formatRupiah(biayaAdmin)}</span></td>
                <td><strong style="color: #e74c3c; font-size: 1.1em;">${formatRupiah(totalDenganTambahan)}</strong></td>
              </tr>
            `;
          });

          table += `
              </tbody>
            </table>
          `;

          hasilDiv.innerHTML = infoHTML + table + `
            <button class="btn-cetak" onclick="cetakTagihan()">🖨️ Cetak Tagihan</button>
          `;

          // Simpan data untuk print
          window.dataPelanggan = plg;
        } else {
          hasilDiv.innerHTML = '<div class="error">❌ Data tidak ditemukan. Pastikan nomor sambungan sudah benar.</div>';
        }
      } catch (error) {
        console.error('Error:', error);
        hasilDiv.innerHTML = `<div class="error">⚠️ Terjadi kesalahan: ${error.message}</div>`;
      }
    });

    // Auto focus pada input
    document.getElementById("nosal").focus();

    // Fungsi untuk cetak tagihan
    function cetakTagihan() {
      if (!window.dataPelanggan) {
        alert('Data pelanggan tidak tersedia');
        return;
      }

      const plg = window.dataPelanggan;
      const biayaAdmin = 11000;
      const danaMeter = 1000;

      // Buat area print
      const printArea = document.createElement('div');
      printArea.className = 'print-area';
      printArea.style.display = 'none';

      let printHTML = `
        <div class="print-header">
          <div class="print-logo">
            TIRTA<br>DHARMA
          </div>
          <div class="print-header-text">
            <h1>Perusahaan Umum Daerah Air Minum</h1>
            <h2>PERUMDA Air Minum Trunojoyo</h2>
            <p><strong>Kabupaten Sampang</strong></p>
            <p>Jl. Rajawali, No. 38, Bledanah, Karang Dalam, Kec. Sampang, Kabupaten Sampang Telp.</p>
            <p>+62323321005</p>
            <p>Sampang 69212</p>
          </div>
        </div>

        <div class="print-title">TAGIHAN REKENING AIR</div>

        <div class="detail-pelanggan">
          <h3>DETAIL PELANGGAN</h3>
          <table class="detail-table">
            <tr>
              <td class="label">NO.SAMBUNGAN</td>
              <td class="dots">: ${plg.nosal}</td>
            </tr>
            <tr>
              <td class="label">NAMA</td>
              <td class="dots">: ${plg.nama}</td>
            </tr>
            <tr>
              <td class="label">ALAMAT</td>
              <td class="dots">: ${plg.alamat}</td>
            </tr>
            <tr>
              <td class="label">RT</td>
              <td class="dots">:</td>
            </tr>
            <tr>
              <td class="label">RW</td>
              <td class="dots">:</td>
            </tr>
            <tr>
              <td class="label">ABM</td>
              <td class="dots">: 010101</td>
            </tr>
            <tr>
              <td class="label">GOLONGAN</td>
              <td class="dots">: ${plg.tagihan[0]?.golongan || 'A-'}</td>
            </tr>
          </table>
        </div>

        <div class="tagihan-section">
          <h3>TAGIHAN AIR</h3>
          <table class="tagihan-table">
            <thead>
              <tr>
                <th>PERIODE</th>
                <th>PAKAI</th>
                <th>HARGA AIR</th>
                <th>D METER</th>
                <th>B ADMIN</th>
                <th>DENDA</th>
                <th>ANGSURAN</th>
                <th>POTONGAN</th>
                <th>MATERAI</th>
                <th>TOTAL</th>
              </tr>
            </thead>
            <tbody>
      `;

      let totalKeseluruhan = 0;

      plg.tagihan.forEach(row => {
        const standAwal = parseInt(row.stand_awal) || 0;
        const standAkhir = parseInt(row.stand_akhir) || 0;
        const pakaiSebenarnya = standAkhir - standAwal;
        const hargaAir = parseInt(row.harga_air) || 0;
        const totalRow = hargaAir + biayaAdmin + danaMeter;
        totalKeseluruhan += totalRow;

        printHTML += `
          <tr>
            <td>Agt 2025</td>
            <td>${pakaiSebenarnya}</td>
            <td>${hargaAir.toLocaleString()}</td>
            <td>${danaMeter.toLocaleString()}</td>
            <td>${biayaAdmin.toLocaleString()}</td>
            <td>0</td>
            <td>0</td>
            <td>0</td>
            <td>0</td>
            <td>${totalRow.toLocaleString()}</td>
          </tr>
        `;
      });

      printHTML += `
            <tr class="total-row">
              <td colspan="9">TOTAL</td>
              <td>${totalKeseluruhan.toLocaleString()}</td>
            </tr>
          </tbody>
        </table>
      </div>
      `;

      printArea.innerHTML = printHTML;
      document.body.appendChild(printArea);

      // Print
      window.print();

      // Hapus area print setelah print
      setTimeout(() => {
        document.body.removeChild(printArea);
      }, 1000);
    }
  </script>
</body>
</html>