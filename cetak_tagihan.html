<!DOCTYPE html>
<html lang="id">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Cetak Tagihan Air</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: Arial, sans-serif;
      font-size: 12px;
      line-height: 1.2;
      padding: 20px;
      background: white;
    }

    .print-container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
    }

    .print-header {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      border-bottom: 3px solid #000;
      padding-bottom: 15px;
    }

    .print-logo {
      width: 80px;
      height: 80px;
      margin-right: 20px;
      background: #2e8b57;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
      font-size: 10px;
      text-align: center;
      line-height: 1.1;
    }

    .print-header-text {
      flex: 1;
      text-align: center;
    }

    .print-header h1 {
      font-size: 14px;
      font-weight: bold;
      margin: 2px 0;
    }

    .print-header h2 {
      font-size: 16px;
      font-weight: bold;
      margin: 2px 0;
    }

    .print-header p {
      font-size: 10px;
      margin: 1px 0;
    }

    .print-title {
      text-align: center;
      font-weight: bold;
      font-size: 14px;
      margin: 15px 0;
      text-decoration: underline;
    }

    .detail-pelanggan {
      margin: 15px 0;
    }

    .detail-pelanggan h3 {
      background: #333;
      color: white;
      padding: 4px 8px;
      margin: 0 0 0 0;
      font-size: 11px;
      font-weight: bold;
      text-transform: uppercase;
    }

    .detail-table {
      width: 100%;
      border: 1px solid #000;
      border-collapse: collapse;
      margin-bottom: 15px;
    }

    .detail-table td {
      border: 1px solid #000;
      padding: 4px 8px;
      font-size: 10px;
      vertical-align: top;
    }

    .detail-table .label {
      width: 120px;
      font-weight: bold;
      background: #f5f5f5;
    }

    .detail-table .dots {
      border-bottom: 1px dotted #000;
      min-height: 15px;
    }

    .tagihan-section h3 {
      background: #333;
      color: white;
      padding: 4px 8px;
      margin: 15px 0 0 0;
      font-size: 11px;
      font-weight: bold;
      text-transform: uppercase;
    }

    .tagihan-table {
      width: 100%;
      border: 1px solid #000;
      border-collapse: collapse;
      margin-top: 0;
    }

    .tagihan-table th,
    .tagihan-table td {
      border: 1px solid #000;
      padding: 4px;
      text-align: center;
      font-size: 9px;
      font-weight: normal;
    }

    .tagihan-table th {
      background: #333;
      color: white;
      font-weight: bold;
      text-transform: uppercase;
    }

    .tagihan-table .total-row {
      background: #e0e0e0;
      font-weight: bold;
    }

    .tagihan-table .total-row td:first-child {
      text-align: center;
      font-weight: bold;
    }

    .btn-container {
      text-align: center;
      margin: 20px 0;
    }

    .btn {
      background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
      color: white;
      padding: 12px 25px;
      border: none;
      border-radius: 8px;
      font-size: 14px;
      cursor: pointer;
      margin: 0 10px;
      transition: transform 0.2s;
    }

    .btn:hover {
      transform: translateY(-2px);
    }

    .btn-cetak {
      background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
    }

    @media print {
      .btn-container {
        display: none;
      }

      .form-container {
        display: none;
      }

      body {
        padding: 0;
      }

      .print-container {
        max-width: none;
        margin: 0;
      }

      #hasil {
        display: block !important;
      }
    }

    .form-container {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 20px;
    }

    .form-group {
      margin-bottom: 15px;
    }

    .form-group label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }

    .form-group input {
      width: 100%;
      padding: 10px;
      border: 2px solid #ddd;
      border-radius: 5px;
      font-size: 14px;
    }

    .form-group input:focus {
      outline: none;
      border-color: #3498db;
    }

    .error {
      background: #f8d7da;
      color: #721c24;
      padding: 15px;
      border-radius: 8px;
      margin: 20px 0;
      text-align: center;
    }

    .loading {
      background: #d1ecf1;
      color: #0c5460;
      padding: 15px;
      border-radius: 8px;
      margin: 20px 0;
      text-align: center;
    }
  </style>
</head>
<body>
  <div class="print-container">
    <div class="form-container">
      <h2>🖨️ Cetak Tagihan Air</h2>
      <p>Masukkan nomor sambungan untuk mencetak tagihan</p>
      
      <form id="cekForm">
        <div class="form-group">
          <label for="nosal">Nomor Sambungan:</label>
          <input type="text" id="nosal" name="nosal" placeholder="Contoh: 0101010020" required>
        </div>
        <div class="btn-container">
          <button type="submit" class="btn">🔍 Cari Data</button>
        </div>
      </form>

      <div id="hasil" style="display: none;"></div>
    </div>
  </div>

  <script>
    const API_URL = "https://satupintu.kopkar.my.id/api/proxy-cektagihan.php";
    const API_KEY = "e8a3b5c7d9f1g2h4i5j6k7l8m9n0o1p2";

    document.getElementById("cekForm").addEventListener("submit", async function(e) {
      e.preventDefault();
      const nosal = document.getElementById("nosal").value.trim();
      const hasilDiv = document.getElementById("hasil");

      // Tampilkan loading
      hasilDiv.style.display = "block";
      hasilDiv.innerHTML = '<div class="loading">🔄 Sedang mencari data tagihan...</div>';

      try {
        let response;
        let result;

        try {
          response = await fetch(API_URL, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "X-API-KEY": API_KEY
            },
            body: JSON.stringify({ nosambungan: nosal })
          });
          result = await response.json();
        } catch (corsError) {
          response = await fetch(API_URL, {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              nosambungan: nosal,
              api_key: API_KEY
            })
          });
          result = await response.json();
        }

        if (result.success && result.plg && result.plg.tagihan && result.plg.tagihan.length > 0) {
          const plg = result.plg;
          tampilkanTagihan(plg);
        } else {
          hasilDiv.innerHTML = '<div class="error">❌ Data tidak ditemukan. Pastikan nomor sambungan sudah benar.</div>';
        }
      } catch (error) {
        console.error('Error:', error);
        hasilDiv.innerHTML = `<div class="error">⚠️ Terjadi kesalahan: ${error.message}</div>`;
      }
    });

    function tampilkanTagihan(plg) {
      const biayaAdmin = 11000;
      const danaMeter = 1000;
      let totalKeseluruhan = 0;

      let tagihanHTML = '';
      plg.tagihan.forEach(row => {
        const standAwal = parseInt(row.stand_awal) || 0;
        const standAkhir = parseInt(row.stand_akhir) || 0;
        const pakaiSebenarnya = standAkhir - standAwal;
        const hargaAir = parseInt(row.harga_air) || 0;
        const totalRow = hargaAir + biayaAdmin + danaMeter;
        totalKeseluruhan += totalRow;

        tagihanHTML += `
          <tr>
            <td>Agt 2025</td>
            <td>${pakaiSebenarnya}</td>
            <td>${hargaAir.toLocaleString()}</td>
            <td>${danaMeter.toLocaleString()}</td>
            <td>${biayaAdmin.toLocaleString()}</td>
            <td>0</td>
            <td>0</td>
            <td>0</td>
            <td>0</td>
            <td>${totalRow.toLocaleString()}</td>
          </tr>
        `;
      });

      const printHTML = `
        <div class="print-header">
          <div class="print-logo">
            TIRTA<br>DHARMA
          </div>
          <div class="print-header-text">
            <h1>Perusahaan Umum Daerah Air Minum</h1>
            <h2>PERUMDA Air Minum Trunojoyo</h2>
            <p><strong>Kabupaten Sampang</strong></p>
            <p>Jl. Rajawali, No. 38, Bledanah, Karang Dalam, Kec. Sampang, Kabupaten Sampang Telp.</p>
            <p>+62323321005</p>
            <p>Sampang 69212</p>
          </div>
        </div>

        <div class="print-title">TAGIHAN REKENING AIR</div>

        <div class="detail-pelanggan">
          <h3>DETAIL PELANGGAN</h3>
          <table class="detail-table">
            <tr>
              <td class="label">NO.SAMBUNGAN</td>
              <td class="dots">: ${plg.nosal}</td>
            </tr>
            <tr>
              <td class="label">NAMA</td>
              <td class="dots">: ${plg.nama}</td>
            </tr>
            <tr>
              <td class="label">ALAMAT</td>
              <td class="dots">: ${plg.alamat}</td>
            </tr>
            <tr>
              <td class="label">RT</td>
              <td class="dots">:</td>
            </tr>
            <tr>
              <td class="label">RW</td>
              <td class="dots">:</td>
            </tr>
            <tr>
              <td class="label">ABM</td>
              <td class="dots">: 010101</td>
            </tr>
            <tr>
              <td class="label">GOLONGAN</td>
              <td class="dots">: ${plg.tagihan[0]?.golongan || 'A-'}</td>
            </tr>
          </table>
        </div>

        <div class="tagihan-section">
          <h3>TAGIHAN AIR</h3>
          <table class="tagihan-table">
            <thead>
              <tr>
                <th>PERIODE</th>
                <th>PAKAI</th>
                <th>HARGA AIR</th>
                <th>D METER</th>
                <th>B ADMIN</th>
                <th>DENDA</th>
                <th>ANGSURAN</th>
                <th>POTONGAN</th>
                <th>MATERAI</th>
                <th>TOTAL</th>
              </tr>
            </thead>
            <tbody>
              ${tagihanHTML}
              <tr class="total-row">
                <td colspan="9">TOTAL</td>
                <td>${totalKeseluruhan.toLocaleString()}</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="btn-container">
          <button class="btn btn-cetak" onclick="window.print()">🖨️ Cetak Sekarang</button>
          <button class="btn" onclick="location.reload()">🔄 Cari Lagi</button>
        </div>
      `;

      document.getElementById("hasil").innerHTML = printHTML;
    }

    // Auto focus pada input
    document.getElementById("nosal").focus();
  </script>
</body>
</html>
